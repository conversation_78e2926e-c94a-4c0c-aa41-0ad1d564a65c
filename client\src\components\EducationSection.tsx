import { motion } from "framer-motion";
import { GraduationCap, School, BookOpen, ChevronRight } from "lucide-react";
import { useState } from "react";

const educationData = [
  {
    icon: <GraduationCap className="w-8 h-8 text-primary-ref" />,
    institution: "Matrusri Engineering College, Hyderabad",
    degree: "Bachelor of Technology - B.E., Computer Science and Engineering",
    duration: "Dec 2021 - Sep 2025",
    grade: "7.01 CGPA",
    level: "Bachelor's",
    description: "I am currently pursuing a Bachelor's degree in Computer Science and Engineering at Matrusri Engineering College, Hyderabad. I have completed 7 semesters and have taken courses in Data Structures, Algorithms, Object-Oriented Programming, Database Management Systems, Web Development, and Machine Learning. I'm actively engaged in various technical clubs and have participated in coding competitions and hackathons."
  },
  {
    icon: <School className="w-8 h-8 text-primary-ref" />,
    institution: "Sri Chaitanya Junior Kalasala, Hyderabad",
    degree: "Telangana State Board of Intermediate Education, MPC",
    duration: "Jun 2019 - Mar 2021",
    grade: "95%",
    level: "Intermediate",
    description: "I completed my Intermediate education at Sri Chaitanya Junior Kalasala, Hyderabad where I studied Mathematics, Physics, and Chemistry (MPC). During this time, I developed a strong foundation in analytical thinking and problem-solving skills."
  },
  {
    icon: <BookOpen className="w-8 h-8 text-primary-ref" />,
    institution: "Narayana High School, Hyderabad",
    degree: "Secondary School Certificate",
    duration: "Jun 2018 - Apr 2019",
    grade: "8.3 CGPA",
    level: "Secondary",
    description: "I completed my class 10 education at Narayana High School, Hyderabad. This foundational education helped me develop discipline, time management skills, and a strong academic foundation."
  }
];

const EducationCard = ({ education, index }: { education: any, index: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <motion.div
      className="relative h-full"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: 0.1 * index }}
    >
      <motion.div
        className={`bg-primary/5 backdrop-blur-sm rounded-xl border border-gray-700/30 overflow-hidden
                   transition-all duration-300 h-full flex flex-col cursor-pointer
                   hover:border-gray-600/50 hover:bg-primary/10 hover:shadow-lg hover:shadow-gray-900/20
                   ${isExpanded ? 'border-gray-600/60 bg-primary/8 shadow-lg shadow-gray-900/10' : ''}`}
        whileHover={{ y: -3, scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {/* Card Header */}
        <div className="p-5 flex flex-col items-center text-center flex-grow">
          {/* Icon and Level Badge */}
          <div className="flex items-center justify-between w-full mb-4">
            <motion.div
              className="bg-gray-800/50 p-3 rounded-lg transition-all duration-300 hover:bg-gray-700/60"
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
            >
              {education.icon}
            </motion.div>
            <motion.span
              className={`text-xs font-medium bg-gray-800/40 px-3 py-1 rounded-full transition-all duration-300
                         ${isExpanded ? 'text-primary-ref bg-gray-700/60' : 'text-muted-ref'}`}
              whileHover={{ scale: 1.05 }}
            >
              {education.level}
            </motion.span>
          </div>

          {/* Institution */}
          <h3 className="text-lg font-semibold text-primary-ref mb-2 leading-tight text-center">
            {education.institution}
          </h3>

          {/* Degree */}
          <p className="text-sm text-secondary-ref mb-3 leading-relaxed text-center">
            {education.degree}
          </p>

          {/* Duration */}
          <div className="mb-3">
            <span className="text-sm text-muted-ref font-medium">{education.duration}</span>
          </div>

          {/* Grade */}
          <motion.div
            className={`bg-gray-800/30 px-4 py-2 rounded-lg transition-all duration-300
                       ${isExpanded ? 'bg-gray-700/50 shadow-md' : ''}`}
            whileHover={{ scale: 1.05, backgroundColor: 'rgba(55, 65, 81, 0.6)' }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="text-muted-ref text-sm">Grade: </span>
            <span className="text-primary-ref font-semibold">{education.grade}</span>
          </motion.div>
        </div>

        {/* Expandable Description */}
        <motion.div
          className="px-5 bg-gray-900/20 overflow-hidden"
          initial={{ height: 0, opacity: 0 }}
          animate={{
            height: isExpanded ? 'auto' : 0,
            opacity: isExpanded ? 1 : 0
          }}
          transition={{ duration: 0.4, ease: "easeInOut" }}
        >
          <motion.div
            className="py-4"
            initial={{ y: 20 }}
            animate={{ y: isExpanded ? 0 : 20 }}
            transition={{ duration: 0.3, delay: isExpanded ? 0.1 : 0 }}
          >
            <p className="text-paragraph-ref text-sm leading-relaxed">{education.description}</p>
          </motion.div>
        </motion.div>

        {/* Expand/Collapse Button */}
        <div className={`px-5 py-3 border-t border-gray-700/20 transition-all duration-300
                        ${isExpanded ? 'bg-gray-900/20' : 'bg-gray-900/10'}`}>
          <motion.button
            className="w-full flex items-center justify-center gap-2 text-sm font-medium text-secondary-ref
                       hover:text-primary-ref transition-colors duration-200 py-1 px-3 rounded-md
                       hover:bg-gray-800/30"
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <motion.span
              key={isExpanded ? 'less' : 'more'}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
            >
              {isExpanded ? 'Show Less' : 'Show More'}
            </motion.span>
            <motion.div
              animate={{
                rotate: isExpanded ? 90 : 0,
                scale: isExpanded ? 1.1 : 1
              }}
              transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
            >
              <ChevronRight className="h-4 w-4" />
            </motion.div>
          </motion.button>
        </div>
      </motion.div>
    </motion.div>
  );
};

const EducationSection = () => {
  return (
    <section id="education" className="pt-10 pb-10 px-6 bg-dark">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl text-display text-elegant mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Education
          </motion.h2>

          <motion.p
            className="text-paragraph-ref text-lg md:text-xl mt-6 mb-8 max-w-4xl mx-auto px-4 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Here's a summary of the educational path that has contributed to my growth and learning mindset.
          </motion.p>

          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-300 mx-auto rounded-full"
            initial={{ width: 0 }}
            whileInView={{ width: 96 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {educationData.map((education, index) => (
            <EducationCard key={index} education={education} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default EducationSection; 