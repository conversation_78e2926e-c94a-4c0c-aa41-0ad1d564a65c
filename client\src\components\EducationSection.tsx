import { motion } from "framer-motion";
import { GraduationCap, School, BookOpen, ChevronRight } from "lucide-react";
import { useState } from "react";

const educationData = [
  {
    icon: <GraduationCap className="w-14 h-14 text-accent" />,
    institution: "Matrusri Engineering College, Hyderabad",
    degree: "Bachelor of Technology - B.E., Computer Science and Engineering",
    duration: "Dec 2021 - Sep 2025",
    grade: "7.01 CGPA",
    description: "I am currently pursuing a Bachelor's degree in Computer Science and Engineering at Matrusri Engineering College, Hyderabad. I have completed 7 semesters and have taken courses in Data Structures, Algorithms, Object-Oriented Programming, Database Management Systems, Web Development, and Machine Learning. I'm actively engaged in various technical clubs and have participated in coding competitions and hackathons."
  },
  {
    icon: <School className="w-14 h-14 text-accent" />,
    institution: "Sri Chaitanya Junior Kalasala, Hyderabad",
    degree: "Telangana State Board of Intermediate Education, MPC",
    duration: "Jun 2019 - Mar 2021",
    grade: "95%",
    description: "I completed my Intermediate education at Sri Chaitanya Junior Kalasala, Hyderabad where I studied Mathematics, Physics, and Chemistry (MPC). During this time, I developed a strong foundation in analytical thinking and problem-solving skills."
  },
  {
    icon: <BookOpen className="w-14 h-14 text-accent" />,
    institution: "Narayana High School, Hyderabad",
    degree: "Secondary School Certificate",
    duration: "Jun 2018 - Apr 2019",
    grade: "8.3 CGPA",
    description: "I completed my class 10 education at Narayana High School, Hyderabad. This foundational education helped me develop discipline, time management skills, and a strong academic foundation."
  }
];

const EducationCard = ({ education, index }: { education: any, index: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <motion.div 
      className="relative h-full"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: 0.1 * index }}
    >
      <motion.div 
        className={`bg-primary/10 backdrop-blur-sm rounded-xl border border-accent/20 overflow-hidden 
                   transition-all duration-500 h-full flex flex-col
                   ${isExpanded ? 'scale-105 shadow-lg shadow-accent/10' : 'scale-100'}`}
        whileHover={{ 
          scale: 1.03,
          borderColor: 'rgba(74, 222, 222, 0.4)',
          boxShadow: '0 0 20px rgba(74, 222, 222, 0.2)'
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {/* Card Header - Fixed height */}
        <div className="p-6 flex flex-col items-center text-center flex-grow">
          <div className="bg-dark/50 p-4 rounded-full mb-4 ring-2 ring-accent/20 hover:ring-accent/60 transition-all duration-300">
            {education.icon}
          </div>
          <h3 className="text-xl md:text-2xl text-heading text-light mb-3 line-clamp-2">{education.institution}</h3>
          <p className="text-body text-neutral text-base line-clamp-2 leading-relaxed">{education.degree}</p>
          <div className="my-3 flex items-center">
            <span className="text-accent font-semibold text-sm">{education.duration}</span>
          </div>
          <div className="bg-dark/30 px-4 py-2 rounded-full mt-auto">
            <span className="text-light font-semibold">Grade: </span>
            <span className="text-accent">{education.grade}</span>
          </div>
        </div>
          
        {/* Expandable Section */}
        <motion.div
          className="p-6 bg-dark/30 overflow-hidden"
          initial={{ height: 0, opacity: 0 }}
          animate={{ 
            height: isExpanded ? 'auto' : 0, 
            opacity: isExpanded ? 1 : 0
          }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-neutral leading-relaxed">{education.description}</p>
        </motion.div>
          
        {/* Card Footer / Expand Button */}
        <div className="px-6 py-3 flex justify-center bg-dark/20 mt-auto">
          <button 
            className="text-accent flex items-center gap-1 text-sm font-medium"
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
          >
            <span>{isExpanded ? 'Show Less' : 'Show More'}</span>
            <motion.div 
              animate={{ rotate: isExpanded ? 90 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <ChevronRight className="h-4 w-4" />
            </motion.div>
          </button>
        </div>
      </motion.div>
    </motion.div>
  );
};

const EducationSection = () => {
  return (
    <section id="education" className="pt-10 pb-10 px-6 bg-dark">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl text-display text-elegant mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Education
          </motion.h2>

          <motion.p
            className="text-body text-light text-xl md:text-2xl lg:text-3xl mt-6 mb-10 max-w-4xl mx-auto px-4 leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Here's a summary of the educational path that has contributed to my growth and learning mindset.
          </motion.p>

          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-accent to-secondary mx-auto rounded-full"
            initial={{ width: 0 }}
            whileInView={{ width: 96 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
          {educationData.map((education, index) => (
            <EducationCard key={index} education={education} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default EducationSection; 