import { motion } from "framer-motion";
import SkillCard from "./SkillCard";

interface SkillCategoryProps {
  title: string;
  skills: { icon: string; name: string }[];
  delay: number;
}

const SkillCategory = ({ title, skills, delay }: SkillCategoryProps) => {
  return (
    <motion.div
      className="rounded-xl bg-dark/30 p-5 border border-accent/10 backdrop-blur-sm h-full flex flex-col
                 hover:border-accent/30 transition-colors duration-500"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
    >
      <h3 className="text-2xl md:text-3xl text-heading text-elegant mb-6 text-center">
        {title}
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 flex-grow">
        {skills.map((skill, index) => (
          <SkillCard
            key={index}
            icon={skill.icon}
            name={skill.name}
            delay={delay + 0.1 + index * 0.05}
          />
        ))}
      </div>
    </motion.div>
  );
};

const SkillsSection = () => {
  const programmingLanguages = [
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/c/c-original.svg" width="22" height="22" />', name: 'C' },
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/cplusplus/cplusplus-original.svg" width="22" height="22" />', name: 'C++' },
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg" width="22" height="22" />', name: 'Python' },
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg" width="22" height="22" />', name: 'Java' }
  ];

  const machineLearning = [
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/tensorflow/tensorflow-original.svg" width="22" height="22" />', name: 'TensorFlow' },
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/keras/keras-original.svg" width="22" height="22" />', name: 'Keras' },
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/pytorch/pytorch-original.svg" width="22" height="22" />', name: 'PyTorch' },
    { icon: '<img src="https://upload.wikimedia.org/wikipedia/commons/0/05/Scikit_learn_logo_small.svg" width="22" height="22" />', name: 'Scikit-Learn' },
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/pandas/pandas-original.svg" width="22" height="22" />', name: 'Pandas' },
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/numpy/numpy-original.svg" width="22" height="22" />', name: 'NumPy' }
  ];

  const dataVisualization = [
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/matplotlib/matplotlib-original.svg" width="22" height="22" />', name: 'Matplotlib' },
    { icon: `<svg width="22" height="22" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="50" cy="50" r="48" fill="white" stroke="#4B5563" stroke-width="2"/>
      <path d="M10 50C10 50 25 20 50 20C75 20 90 50 90 50V85H10V50Z" fill="#86C1CF"/>
      <path d="M10 60C10 60 25 40 50 40C75 40 90 60 90 60V85H10V60Z" fill="#6096B4"/>
      <path d="M10 70C10 70 25 55 50 55C75 55 90 70 90 70V85H10V70Z" fill="#3C6382"/>
      <path d="M10 85V80H20V85H10Z" fill="#2E4C6D"/>
      <path d="M20 85V75H30V85H20Z" fill="#2E4C6D"/>
      <path d="M30 85V70H40V85H30Z" fill="#2E4C6D"/>
      <path d="M40 85V75H50V85H40Z" fill="#2E4C6D"/>
      <path d="M50 85V80H60V85H50Z" fill="#2E4C6D"/>
      <path d="M10 50C10 50 25 30 40 30C55 30 60 35 70 35C80 35 90 30 90 30" stroke="#FFFFFF" stroke-width="1" stroke-dasharray="2 2"/>
    </svg>`, name: 'Seaborn' },
    { icon: `<svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="24" height="24" rx="4" fill="#111827"/>
      <circle cx="6" cy="6" r="1.5" fill="#3B82F6"/>
      <circle cx="10" cy="6" r="1.5" fill="#3B82F6"/>
      <circle cx="14" cy="6" r="1.5" fill="#60A5FA"/>
      <circle cx="18" cy="6" r="1.5" fill="#93C5FD"/>
      <rect x="5" y="9" width="2" height="10" rx="1" fill="white"/>
      <rect x="9" y="12" width="2" height="7" rx="1" fill="white"/>
      <rect x="13" y="10" width="2" height="9" rx="1" fill="white"/>
      <rect x="17" y="13" width="2" height="6" rx="1" fill="white"/>
    </svg>`, name: 'Plotly' },
    { icon: '<img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/d3js/d3js-original.svg" width="22" height="22" />', name: 'D3.js' }
  ];

  return (
    <section id="skills" className="pt-10 pb-10 px-6 bg-dark">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl text-display text-elegant mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Skills
          </motion.h2>

          <motion.p
            className="text-paragraph-ref text-lg md:text-xl max-w-3xl mx-auto"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Here are the technologies and tools I've mastered throughout my journey in AI and machine learning. I'm constantly expanding this toolkit to stay at the cutting edge of data science and artificial intelligence.
          </motion.p>

          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-accent to-secondary mx-auto rounded-full mt-6"
            initial={{ width: 0 }}
            whileInView={{ width: 96 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          />
        </div>
        
        <div className="grid md:grid-cols-3 gap-6">
          <SkillCategory 
            title="Programming Languages" 
            skills={programmingLanguages} 
            delay={0.2} 
          />
          <SkillCategory 
            title="Machine Learning" 
            skills={machineLearning} 
            delay={0.3} 
          />
          <div className="rounded-xl bg-dark/30 p-5 border border-accent/10 backdrop-blur-sm h-full
                          hover:border-accent/30 transition-all duration-500 group">
            <motion.div
              className="h-full flex flex-col"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <h3 className="text-2xl font-bold text-light mb-5 text-center">
                <span className="bg-gradient-to-r from-accent to-secondary bg-clip-text text-transparent">
                  Data Visualization
                </span>
              </h3>
              <div className="grid grid-cols-2 gap-3 flex-grow">
                {dataVisualization.map((skill, index) => (
                  <SkillCard
                    key={index}
                    icon={skill.icon}
                    name={skill.name}
                    delay={0.4 + 0.1 + index * 0.05}
                  />
                ))}
              </div>
              <div className="mt-4 h-1 w-0 mx-auto bg-gradient-to-r from-accent to-secondary 
                              group-hover:w-full transition-all duration-700 rounded-full"></div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SkillsSection; 