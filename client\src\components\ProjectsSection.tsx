import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Github, ExternalLink, ArrowRight } from "lucide-react";

const projects = [
  {
    id: 1,
    title: "E-commerce Platform",
    description: "A full-featured e-commerce platform with product catalog, cart functionality, and secure checkout process.",
    image: "https://images.unsplash.com/photo-1586880244406-556ebe35f282?auto=format&fit=crop&w=800&h=400",
    techStack: ["React", "Node.js", "MongoDB", "Express"],
    githubLink: "#",
    liveLink: "#"
  },
  {
    id: 2,
    title: "Task Manager",
    description: "A productivity application for task management with features like drag-and-drop organization, priority levels, and deadline notifications.",
    image: "https://images.unsplash.com/photo-1555421689-d68471e189f2?auto=format&fit=crop&w=800&h=400",
    techStack: ["React", "Firebase", "Redux", "CSS Modules"],
    githubLink: "#",
    liveLink: "#"
  },
  {
    id: 3,
    title: "Weather Dashboard",
    description: "A weather application that displays current conditions and forecasts based on location, with beautiful visualizations of weather data.",
    image: "https://images.unsplash.com/photo-1592210454359-9043f067919b?auto=format&fit=crop&w=800&h=400",
    techStack: ["JavaScript", "OpenWeather API", "Chart.js", "CSS"],
    githubLink: "#",
    liveLink: "#"
  }
];

const ProjectsSection = () => {
  return (
    <section id="projects" className="pt-10 pb-10 px-6 bg-dark">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl text-display text-elegant mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            My Projects
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-300 mx-auto rounded-full"
            initial={{ width: 0 }}
            whileInView={{ width: 96 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
          />
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="project-card"
            >
              <Card className="bg-primary rounded-lg overflow-hidden shadow-lg transition-all duration-500 
                               hover:-translate-y-2 hover:shadow-xl hover:shadow-accent/20 relative group">
                <div className="relative h-48 overflow-hidden">
                  <img 
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-dark/40 group-hover:bg-gradient-to-r group-hover:from-accent/30 group-hover:to-secondary/30 transition-all duration-500"></div>
                </div>
                <CardContent className="p-6 space-y-4">
                  <h3 className="text-xl md:text-2xl text-heading text-primary-ref group-hover:text-elegant transition-colors duration-300">{project.title}</h3>
                  <p className="text-paragraph-ref line-clamp-3">{project.description}</p>
                  <div className="flex flex-wrap gap-2 pt-2">
                    {project.techStack.map((tech, techIndex) => (
                      <span key={techIndex} className="px-2 py-1 bg-dark/40 text-accent text-xs rounded
                                                       hover:bg-accent/20 hover:scale-105 transition-all duration-300">
                        {tech}
                      </span>
                    ))}
                  </div>
                  <div className="flex justify-between pt-4">
                    <a 
                      href={project.githubLink} 
                      className="text-accent hover:text-secondary transition-all duration-300 flex items-center gap-2
                                group/link hover:-translate-y-1"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Github className="h-4 w-4" />
                      <span>Code</span>
                      <span className="block w-0 h-0.5 bg-secondary group-hover/link:w-full transition-all duration-300"></span>
                    </a>
                    <a 
                      href={project.liveLink} 
                      className="text-accent hover:text-secondary transition-all duration-300 flex items-center gap-2
                                group/link hover:-translate-y-1"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="h-4 w-4" />
                      <span>Live Demo</span>
                      <span className="block w-0 h-0.5 bg-secondary group-hover/link:w-full transition-all duration-300"></span>
                    </a>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
        
        <div className="flex justify-center mt-12">
          <Button 
            variant="outline"
            className="bg-dark border border-accent text-accent hover:bg-gradient-to-r hover:from-accent/20 hover:to-secondary/20 
                       font-medium px-6 py-6 rounded-md inline-flex items-center gap-2 
                       transition-all duration-500 hover:-translate-y-1 hover:shadow-lg hover:shadow-accent/10
                       hover:border-transparent group"
          >
            <span className="group-hover:text-secondary transition-colors duration-300">View More Projects</span>
            <ArrowRight className="h-4 w-4 group-hover:text-secondary transition-colors duration-300 
                                  transform group-hover:translate-x-1 transition-transform duration-300" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;
