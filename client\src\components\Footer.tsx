import { Gith<PERSON>, <PERSON>ed<PERSON>, Twitter, Instagram } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="py-8 px-6 bg-primary border-t border-neutral/10">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="text-muted-ref mb-4 md:mb-0">
            &copy; {currentYear} T Krishna Preetham. All Rights Reserved.
          </div>
          
          <div className="flex gap-6">
            <a 
              href="https://linkedin.com/in/tkrishnapreetham" 
              className="social-icon text-muted-ref text-lg transition-all hover:text-primary-ref hover:-translate-y-1"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="LinkedIn"
            >
              <Linkedin className="h-5 w-5" />
            </a>
            <a 
              href="https://github.com/tkrishnapreetham" 
              className="social-icon text-muted-ref text-lg transition-all hover:text-primary-ref hover:-translate-y-1"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="GitHub"
            >
              <Github className="h-5 w-5" />
            </a>
            <a 
              href="#" 
              className="social-icon text-muted-ref text-lg transition-all hover:text-primary-ref hover:-translate-y-1"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Twitter"
            >
              <Twitter className="h-5 w-5" />
            </a>
            <a 
              href="#" 
              className="social-icon text-muted-ref text-lg transition-all hover:text-primary-ref hover:-translate-y-1"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Instagram"
            >
              <Instagram className="h-5 w-5" />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
