import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { useMobileMenu } from "@/hooks/use-mobile-menu";

interface NavbarProps {
  activeSection: string;
  setActiveSection: (section: string) => void;
}

const Navbar = ({ activeSection, setActiveSection }: NavbarProps) => {
  const [scrolled, setScrolled] = useState(false);
  const { mobileMenuOpen, toggleMobileMenu, closeMobileMenu } = useMobileMenu();

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    closeMobileMenu();
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <header className={`fixed w-full z-50 transition-all duration-300 bg-dark/90 backdrop-blur ${scrolled ? 'py-2' : 'py-4'}`}>
      <nav className="container mx-auto px-6 flex justify-between items-center">
        <a 
          href="#hero" 
          className="text-2xl font-bold text-accent"
          onClick={(e) => {
            e.preventDefault();
            scrollToSection("hero");
          }}
        >
          Welcome, Friend!
        </a>
        
        <Button 
          variant="ghost" 
          size="icon" 
          className="lg:hidden text-light"
          onClick={toggleMobileMenu}
          aria-label="Toggle menu"
        >
          {mobileMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </Button>
        
        <div className="hidden lg:flex items-center space-x-10">
          {["about", "skills", "projects", "education", "resume", "contact"].map((section) => (
            <a
              key={section}
              href={`#${section}`}
              className={`nav-link text-light hover:text-accent transition-colors relative ${
                activeSection === section ? "text-accent" : ""
              }`}
              onClick={(e) => {
                e.preventDefault();
                scrollToSection(section);
              }}
            >
              {section.charAt(0).toUpperCase() + section.slice(1)}
              <span className={`absolute bottom-0 left-0 w-0 h-0.5 bg-accent transition-all duration-300 ${
                activeSection === section ? "w-full" : ""
              }`}></span>
            </a>
          ))}
        </div>
      </nav>
      
      <div className={`px-6 py-4 bg-primary/90 lg:hidden ${mobileMenuOpen ? "block" : "hidden"}`}>
        <div className="flex flex-col space-y-4">
          {["about", "skills", "projects", "education", "resume", "contact"].map((section) => (
            <a
              key={section}
              href={`#${section}`}
              className={`text-light hover:text-accent transition-colors py-2 ${
                activeSection === section ? "text-accent" : ""
              }`}
              onClick={(e) => {
                e.preventDefault();
                scrollToSection(section);
              }}
            >
              {section.charAt(0).toUpperCase() + section.slice(1)}
            </a>
          ))}
        </div>
      </div>
    </header>
  );
};

export default Navbar;
