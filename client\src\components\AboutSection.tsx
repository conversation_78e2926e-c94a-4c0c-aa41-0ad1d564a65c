import { motion } from "framer-motion";

const AboutSection = () => {
  return (
    <section id="about" className="pt-10 pb-10 px-6 bg-primary">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl text-display text-elegant mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            About Me
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-300 mx-auto rounded-full"
            initial={{ width: 0 }}
            whileInView={{ width: 96 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
          />
        </div>
        
        <motion.div
          className="space-y-8 max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <p className="text-paragraph-ref text-lg md:text-xl text-center">
            I am a dedicated AI/ML Engineer and Data Analyst with a passion for leveraging data to solve complex problems. My journey into the world of artificial intelligence began with a fascination for how machine learning algorithms can extract meaningful patterns from data and has evolved into a career focused on building intelligent systems.
          </p>
          <p className="text-paragraph-ref text-lg md:text-xl text-center">
            With expertise in both predictive modeling and data analysis, I enjoy transforming raw data into actionable insights and developing AI solutions that make a real-world impact. I stay current with the latest advancements in machine learning research and techniques to continuously improve my approaches.
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutSection;
