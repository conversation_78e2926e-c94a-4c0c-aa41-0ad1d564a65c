@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 222 47% 11%;
  --foreground: 0 0% 98%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 222 47% 11%;
  --popover-foreground: 0 0% 98%;
  --card: 222 47% 11%;
  --card-foreground: 0 0% 98%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --primary: 222 47% 11%;
  --primary-foreground: 0 0% 98%;
  --secondary: 207 90% 54%;
  --secondary-foreground: 211 100% 99%;
  --accent: 192 80% 60%;
  --accent-foreground: 211 100% 99%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 207 90% 54%;
  --radius: 0.5rem;
  
  /* Custom gradient colors */
  --gradient-1: 192 80% 60%; /* Teal/Cyan (accent) */
  --gradient-2: 207 90% 54%; /* Blue (secondary) */
  --gradient-3: 262 83% 58%; /* Purple */
  --gradient-4: 316 70% 50%; /* Magenta/Pink */
  --gradient-5: 150 84% 64%; /* Emerald Green */
}

.dark {
  --background: 222 47% 11%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 222 47% 11%;
  --popover-foreground: 0 0% 98%;
  --card: 222 47% 11%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 222 47% 11%;
  --primary-foreground: 0 0% 98%;
  --secondary: 207 90% 54%;
  --secondary-foreground: 211 100% 99%;
  --accent: 192 80% 60%;
  --accent-foreground: 211 100% 99%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 207 90% 54%;
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variant-numeric: oldstyle-nums;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
  }
  
  /* Custom cursor styles */
  body.custom-cursor-active {
    cursor: none !important;
  }
  
  body.custom-cursor-active a, 
  body.custom-cursor-active button,
  body.custom-cursor-active [role="button"],
  body.custom-cursor-active input,
  body.custom-cursor-active textarea,
  body.custom-cursor-active select {
    cursor: none !important;
  }

  /* Interactive button hover effects */
  button,
  a,
  [role="button"] {
    position: relative;
    transition: all 0.3s ease;
  }
  
  button:active,
  a:active,
  [role="button"]:active {
    transform: scale(0.97);
  }
}

@layer components {
  /* Enhanced Typography Classes */
  .text-display {
    @apply font-display font-bold tracking-tight;
    font-feature-settings: 'ss01', 'ss02';
  }

  .text-heading {
    @apply font-sans font-semibold tracking-tight;
    font-feature-settings: 'ss01';
  }

  .text-body {
    @apply font-sans font-normal leading-relaxed;
    font-feature-settings: 'kern', 'liga';
  }

  .text-caption {
    @apply font-sans font-medium text-sm tracking-wide;
    font-feature-settings: 'ss02';
  }

  .text-mono {
    @apply font-mono font-normal;
    font-feature-settings: 'ss01', 'zero';
  }

  /* Enhanced Gradient Text Effects */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-accent via-secondary to-accent bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradient-shift 6s ease-in-out infinite;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradient-shift 8s ease-in-out infinite;
  }

  .text-gradient-subtle {
    @apply bg-gradient-to-r from-neutral-200 via-white to-neutral-200 bg-clip-text text-transparent;
    background-size: 200% 200%;
  }

  /* Text Shadow Effects */
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
  }

  .text-glow {
    text-shadow: 0 0 10px rgba(74, 222, 222, 0.3), 0 0 20px rgba(74, 222, 222, 0.2);
  }

  .nav-link {
    position: relative;
  }
  
  .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: hsl(var(--accent));
    transition: width 0.3s ease;
  }
  
  .nav-link:hover::after {
    width: 100%;
  }
  
  .social-icon {
    transition: transform 0.3s ease, color 0.3s ease;
  }
  
  .social-icon:hover {
    transform: translateY(-3px);
    color: hsl(var(--accent));
  }
  
  .project-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes text-shimmer {
    0% {
      background-position: -200% center;
    }
    100% {
      background-position: 200% center;
    }
  }

  .text-shimmer {
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%
    );
    background-size: 200% 100%;
    animation: text-shimmer 3s ease-in-out infinite;
    background-clip: text;
    -webkit-background-clip: text;
  }
  
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 4s ease infinite;
  }
  
  .hover-gradient:hover {
    background-size: 200% 200%;
    animation: gradient-shift 2s ease infinite;
  }
  
  .name-gradient {
    background-image: linear-gradient(135deg,
      hsl(var(--gradient-1)) 0%,
      hsl(var(--gradient-3)) 50%,
      hsl(var(--gradient-2)) 100%
    );
    background-size: 300% 300%;
    animation: gradient-shift 6s ease-in-out infinite;
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: 800;
    letter-spacing: -0.02em;
  }

  .job-title-gradient-1 {
    background-image: linear-gradient(135deg,
      hsl(var(--gradient-5)) 0%,
      hsl(var(--gradient-2)) 100%
    );
    background-size: 200% 200%;
    animation: gradient-shift 4s ease-in-out infinite;
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: 700;
    letter-spacing: -0.01em;
  }

  .job-title-gradient-2 {
    background-image: linear-gradient(135deg,
      hsl(var(--gradient-4)) 0%,
      hsl(var(--gradient-3)) 100%
    );
    background-size: 200% 200%;
    animation: gradient-shift 5s ease-in-out infinite;
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: 700;
    letter-spacing: -0.01em;
  }
  
  /* Custom cursor animation */
  @keyframes pulse {
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); opacity: 0.7; }
  }
  
  .cursor {
    mix-blend-mode: difference;
    z-index: 9999;
    pointer-events: none;
    animation: pulse 2s ease-in-out infinite;
  }
  
  /* Interactive button effect */
  .magnetic-button {
    transform-style: preserve-3d;
    transform: perspective(1000px);
  }
  
  .button-content {
    transform: translateZ(20px);
  }
  
  .button-shine {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at var(--mouse-x) var(--mouse-y), 
                               rgba(255, 255, 255, 0.2) 0%, 
                               transparent 50%);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
  }
  
  .magnetic-button:hover .button-shine {
    opacity: 1;
  }
}
