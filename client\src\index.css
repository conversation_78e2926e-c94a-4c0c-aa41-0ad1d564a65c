@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 222 47% 11%;
  --foreground: 0 0% 98%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 222 47% 11%;
  --popover-foreground: 0 0% 98%;
  --card: 222 47% 11%;
  --card-foreground: 0 0% 98%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --primary: 222 47% 11%;
  --primary-foreground: 0 0% 98%;
  --secondary: 207 90% 54%;
  --secondary-foreground: 211 100% 99%;
  --accent: 192 80% 60%;
  --accent-foreground: 211 100% 99%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 207 90% 54%;
  --radius: 0.5rem;
  
  /* Custom gradient colors */
  --gradient-1: 192 80% 60%; /* <PERSON><PERSON>/<PERSON><PERSON> (accent) */
  --gradient-2: 207 90% 54%; /* Blue (secondary) */
  --gradient-3: 262 83% 58%; /* Purple */
  --gradient-4: 316 70% 50%; /* Magenta/Pink */
  --gradient-5: 150 84% 64%; /* Emerald Green */
}

.dark {
  --background: 222 47% 11%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 222 47% 11%;
  --popover-foreground: 0 0% 98%;
  --card: 222 47% 11%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 222 47% 11%;
  --primary-foreground: 0 0% 98%;
  --secondary: 207 90% 54%;
  --secondary-foreground: 211 100% 99%;
  --accent: 192 80% 60%;
  --accent-foreground: 211 100% 99%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 207 90% 54%;
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }

  html {
    scroll-behavior: smooth;
  }
  
  /* Custom cursor styles */
  body.custom-cursor-active {
    cursor: none !important;
  }
  
  body.custom-cursor-active a, 
  body.custom-cursor-active button,
  body.custom-cursor-active [role="button"],
  body.custom-cursor-active input,
  body.custom-cursor-active textarea,
  body.custom-cursor-active select {
    cursor: none !important;
  }

  /* Interactive button hover effects */
  button,
  a,
  [role="button"] {
    position: relative;
    transition: all 0.3s ease;
  }
  
  button:active,
  a:active,
  [role="button"]:active {
    transform: scale(0.97);
  }
}

@layer components {
  .nav-link {
    position: relative;
  }
  
  .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: hsl(var(--accent));
    transition: width 0.3s ease;
  }
  
  .nav-link:hover::after {
    width: 100%;
  }
  
  .social-icon {
    transition: transform 0.3s ease, color 0.3s ease;
  }
  
  .social-icon:hover {
    transform: translateY(-3px);
    color: hsl(var(--accent));
  }
  
  .project-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 4s ease infinite;
  }
  
  .hover-gradient:hover {
    background-size: 200% 200%;
    animation: gradient-shift 2s ease infinite;
  }
  
  .name-gradient {
    background-image: linear-gradient(to right, 
      hsl(var(--gradient-1)), 
      hsl(var(--gradient-3)),
      hsl(var(--gradient-2))
    );
    background-size: 200% 200%;
    animation: gradient-shift 4s ease infinite;
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text;
  }
  
  .job-title-gradient-1 {
    background-image: linear-gradient(to right, 
      hsl(var(--gradient-5)), 
      hsl(var(--gradient-2))
    );
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text;
  }
  
  .job-title-gradient-2 {
    background-image: linear-gradient(to right, 
      hsl(var(--gradient-4)), 
      hsl(var(--gradient-3))
    );
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text;
  }
  
  /* Custom cursor animation */
  @keyframes pulse {
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); opacity: 0.7; }
  }
  
  .cursor {
    mix-blend-mode: difference;
    z-index: 9999;
    pointer-events: none;
    animation: pulse 2s ease-in-out infinite;
  }
  
  /* Interactive button effect */
  .magnetic-button {
    transform-style: preserve-3d;
    transform: perspective(1000px);
  }
  
  .button-content {
    transform: translateZ(20px);
  }
  
  .button-shine {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at var(--mouse-x) var(--mouse-y), 
                               rgba(255, 255, 255, 0.2) 0%, 
                               transparent 50%);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
  }
  
  .magnetic-button:hover .button-shine {
    opacity: 1;
  }
}
