import { useEffect, useRef } from 'react';

const CustomCursor = () => {
  const cursorRef = useRef<HTMLDivElement>(null);
  const isHovering = useRef(false);
  
  useEffect(() => {
    let animationFrameId: number;
    let cursorVisible = false;
    
    // Use requestAnimationFrame for smoother performance
    const updateCursorPosition = (e: MouseEvent) => {
      if (!cursorRef.current) return;
      
      if (!cursorVisible) {
        cursorRef.current.style.opacity = '1';
        cursorVisible = true;
      }
      
      // Direct DOM manipulation instead of state updates
      animationFrameId = requestAnimationFrame(() => {
        if (cursorRef.current) {
          cursorRef.current.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
        }
      });
    };
    
    const handleMouseEnter = () => {
      document.body.style.cursor = 'none';
    };
    
    const handleMouseLeave = () => {
      document.body.style.cursor = 'auto';
      if (cursorRef.current) {
        cursorRef.current.style.opacity = '0';
        cursorVisible = false;
      }
    };
    
    const handleButtonMouseEnter = () => {
      isHovering.current = true;
      if (cursorRef.current) {
        cursorRef.current.classList.add('cursor-hover');
      }
    };
    
    const handleButtonMouseLeave = () => {
      isHovering.current = false;
      if (cursorRef.current) {
        cursorRef.current.classList.remove('cursor-hover');
      }
    };
    
    // Add listeners
    window.addEventListener('mousemove', updateCursorPosition, { passive: true });
    document.addEventListener('mouseenter', handleMouseEnter);
    document.addEventListener('mouseleave', handleMouseLeave);
    
    // Add listeners to all buttons and links
    const interactiveElements = document.querySelectorAll('button, a, [role="button"]');
    interactiveElements.forEach(element => {
      element.addEventListener('mouseenter', handleButtonMouseEnter);
      element.addEventListener('mouseleave', handleButtonMouseLeave);
    });
    
    return () => {
      window.removeEventListener('mousemove', updateCursorPosition);
      document.removeEventListener('mouseenter', handleMouseEnter);
      document.removeEventListener('mouseleave', handleMouseLeave);
      
      interactiveElements.forEach(element => {
        element.removeEventListener('mouseenter', handleButtonMouseEnter);
        element.removeEventListener('mouseleave', handleButtonMouseLeave);
      });
      
      cancelAnimationFrame(animationFrameId);
      document.body.style.cursor = 'auto';
    };
  }, []);
  
  return (
    <div 
      ref={cursorRef}
      className="pointer-events-none fixed top-0 left-0 z-50 opacity-0 cursor-dot"
      style={{
        width: '16px',
        height: '16px',
        border: '2px solid rgb(74, 222, 222)',
        borderRadius: '50%',
        transform: 'translate(-50%, -50%)',
        transition: 'width 0.2s, height 0.2s, opacity 0.2s',
      }}
    />
  );
};

// Add this to your global CSS
const injectGlobalStyles = () => {
  const styleElement = document.createElement('style');
  styleElement.innerHTML = `
    .cursor-dot {
      margin-left: -8px;
      margin-top: -8px;
      mix-blend-mode: difference;
    }
    .cursor-hover {
      width: 40px !important;
      height: 40px !important;
      margin-left: -20px;
      margin-top: -20px;
      background-color: rgba(74, 222, 222, 0.1);
    }
  `;
  document.head.appendChild(styleElement);
};

// Inject styles on module load
if (typeof window !== 'undefined') {
  injectGlobalStyles();
}

export default CustomCursor; 