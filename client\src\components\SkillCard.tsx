import { motion } from "framer-motion";

interface SkillCardProps {
  icon: string;
  name: string;
  delay: number;
}

const SkillCard = ({ icon, name, delay }: SkillCardProps) => {
  return (
    <motion.div
      className="flex items-center space-x-2 bg-dark/60 py-2 px-3 rounded-md border border-accent/20 backdrop-blur 
                 hover:bg-gradient-to-r hover:from-dark/80 hover:to-accent/20 hover:border-accent/50 
                 transition-all duration-300 group cursor-pointer"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.4, delay }}
      whileHover={{ 
        scale: 1.05, 
        boxShadow: "0 10px 25px -5px rgba(78, 204, 216, 0.1), 0 10px 10px -5px rgba(78, 204, 216, 0.04)"
      }}
    >
      <span className="text-xl flex-shrink-0 group-hover:text-accent transition-colors duration-300" dangerouslySetInnerHTML={{ __html: icon }} />
      <span className="text-light text-base group-hover:text-accent transition-colors duration-300">{name}</span>
    </motion.div>
  );
};

export default SkillCard; 