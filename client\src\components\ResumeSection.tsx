import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Download, Eye } from "lucide-react";

const skills = [
  { name: "Frontend Development", percentage: 90 },
  { name: "Backend Development", percentage: 85 },
  { name: "UI/UX Design", percentage: 75 },
  { name: "Database Management", percentage: 80 }
];

const ResumeSection = () => {
  const handleView = () => {
    window.open('/resume.pdf', '_blank');
  };

  const handleDownload = () => {
    // Create a link element
    const link = document.createElement('a');
    link.href = '/resume.pdf';
    link.download = 'Krishna_Preetham_Resume.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section id="resume" className="pt-10 pb-10 px-6 bg-primary">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl text-display text-elegant mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Resume
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-300 mx-auto rounded-full"
            initial={{ width: 0 }}
            whileInView={{ width: 96 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
          />
        </div>
        
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <motion.div 
            className="space-y-6"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h3 className="text-2xl md:text-3xl text-heading text-primary-ref">My Professional Background</h3>
            <p className="text-paragraph-ref text-lg">
              I have a comprehensive resume that outlines my professional experience, projects, educational background, and technical skillset. Feel free to view or download it for a detailed overview of my qualifications.
            </p>
            <div className="flex gap-4 mt-4">
              <Button 
                onClick={handleView}
                className="bg-accent hover:bg-accent/90 text-primary font-medium px-6 py-4 rounded-md inline-flex items-center gap-2 transition-all hover:-translate-y-1"
              >
                <Eye className="h-5 w-5" />
                <span>View Resume</span>
              </Button>
            <Button 
              onClick={handleDownload}
                variant="outline"
                className="border-accent text-accent hover:bg-accent/10 font-medium px-6 py-4 rounded-md inline-flex items-center gap-2 transition-all hover:-translate-y-1"
            >
              <Download className="h-5 w-5" />
                <span>Download</span>
            </Button>
            </div>
          </motion.div>
          
          <motion.div 
            className="bg-dark/50 p-8 rounded-lg"
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h4 className="text-xl md:text-2xl text-heading text-primary-ref mb-6">Skills Overview</h4>
            
            {skills.map((skill, index) => (
              <motion.div 
                key={index} 
                className="mb-6 last:mb-0"
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.3, delay: 0.5 + (index * 0.1) }}
              >
                <div className="flex justify-between mb-2">
                  <span className="text-secondary-ref">{skill.name}</span>
                  <span className="text-primary-ref">{skill.percentage}%</span>
                </div>
                <Progress value={skill.percentage} className="h-2 bg-dark" indicatorClassName="bg-accent" />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ResumeSection;
