import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { User } from "lucide-react";
import { useState, useEffect } from "react";

interface HeroSectionProps {
  setActiveSection: (section: string) => void;
}

const TypingAnimation = ({ jobTitles }: { jobTitles: string[] }) => {
  const [text, setText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [loopNum, setLoopNum] = useState(0);
  const [typingSpeed, setTypingSpeed] = useState(150);

  useEffect(() => {
    const handleTyping = () => {
      const i = loopNum % jobTitles.length;
      const fullText = jobTitles[i];

      if (isDeleting) {
        // Deleting text
        setText(fullText.substring(0, text.length - 1));
        setTypingSpeed(75); // Faster when deleting
      } else {
        // Typing text
        setText(fullText.substring(0, text.length + 1));
        setTypingSpeed(150); // Normal speed when typing
      }

      // Handle state transitions
      if (!isDeleting && text === fullText) {
        // Finished typing full text, wait and start deleting
        setTimeout(() => setIsDeleting(true), 1500);
      } else if (isDeleting && text === '') {
        // Finished deleting, move to next job title
        setIsDeleting(false);
        setLoopNum(loopNum + 1);
        setTypingSpeed(500); // Pause before starting next word
      }
    };

    const timer = setTimeout(handleTyping, typingSpeed);
    return () => clearTimeout(timer);
  }, [text, isDeleting, loopNum, jobTitles, typingSpeed]);

  return (
    <div className="h-12 flex items-center">
      <motion.h2
        className="text-2xl md:text-3xl lg:text-4xl text-heading job-title-gradient-1"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        {text}
        <span className="ml-1 inline-block w-0.5 h-8 bg-accent animate-blink"></span>
      </motion.h2>
    </div>
  );
};

const HeroSection = ({ setActiveSection }: HeroSectionProps) => {
  const jobTitles = ["AI/ML Engineer", "Data Analyst", "Full Stack Python Developer"];

  const handleClick = (sectionId: string) => {
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section id="hero" className="min-h-screen flex items-center pt-16 pb-10 px-6">
      <div className="container mx-auto grid lg:grid-cols-2 gap-8 items-center">
        <motion.div 
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.p
            className="text-accent font-medium text-lg tracking-wide uppercase"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            Hi, my name is
          </motion.p>
          <motion.h1
            className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl text-display text-elegant"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            T Krishna Preetham
          </motion.h1>
          
          <TypingAnimation jobTitles={jobTitles} />
          
          <motion.p
            className="text-body text-neutral text-lg md:text-xl max-w-2xl leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            I specialize in developing intelligent systems using cutting-edge technologies like TensorFlow, Keras, and Python. My work focuses on building data-driven solutions, from predictive models to insightful data visualizations, solving complex problems with machine learning and analytics.
          </motion.p>
          <motion.div 
            className="flex flex-wrap gap-4 pt-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <Button 
              onClick={() => handleClick("projects")}
              className="bg-gradient-to-r from-accent to-secondary hover:from-secondary hover:to-accent 
                         text-primary font-medium px-6 py-6 rounded-md transition-all duration-500 
                         hover:-translate-y-1 hover:shadow-lg hover:shadow-accent/20"
            >
              View My Work
            </Button>
            <Button 
              onClick={() => handleClick("contact")}
              variant="outline" 
              className="border border-accent text-accent hover:bg-accent/10 font-medium px-6 py-6 
                         rounded-md transition-all duration-500 hover:-translate-y-1 
                         hover:shadow-lg hover:shadow-accent/20 hover:border-secondary
                         relative overflow-hidden group"
            >
              <span className="relative z-10">Contact Me</span>
              <div className="absolute inset-0 w-0 bg-gradient-to-r from-accent/10 to-secondary/10 
                              transition-all duration-500 group-hover:w-full"></div>
            </Button>
          </motion.div>
        </motion.div>
        <motion.div 
          className="hidden lg:flex justify-end"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
        >
          <div className="relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-accent to-secondary rounded-lg blur opacity-40"></div>
            <div className="relative bg-primary p-8 rounded-lg flex items-center justify-center aspect-square w-80 h-80">
              <Avatar className="w-64 h-64 rounded-lg">
                <AvatarFallback className="bg-primary border-2 border-accent text-accent rounded-lg">
                  <User className="w-32 h-32" />
                </AvatarFallback>
              </Avatar>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;
